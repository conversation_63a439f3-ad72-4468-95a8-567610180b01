import { Injectable, UnauthorizedException, ConflictException, Inject } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import * as bcrypt from "bcryptjs";
import { IAuthRepository } from "../models/interfaces/auth-repository.interface";
import { LoginDto } from "../models/dtos/login.dto";
import { RegisterDto } from "../models/dtos/register.dto";
import { AuthResponseDto } from "../models/dtos/auth-response.dto";
import { UserResponseDto } from "../../user/models/dtos/user-response.dto";
import { User } from "../../user/models/entities/user.entity";

@Injectable()
export class AuthService {
	constructor(
		@Inject("IAuthRepository")
		private readonly authRepository: IAuthRepository,
		private readonly jwtService: JwtService
	) {}

	async validateUser(usernameOrEmail: string, password: string): Promise<any> {
		const user = await this.authRepository.findByUsernameOrEmail(usernameOrEmail);
		if (user && (await bcrypt.compare(password, user.password))) {
			const { password, ...result } = user;
			return result;
		}
		return null;
	}

	async login(loginDto: LoginDto): Promise<AuthResponseDto> {
		const user = await this.validateUser(loginDto.usernameOrEmail, loginDto.password);
		if (!user) {
			throw new UnauthorizedException("Credenciais inválidas");
		}

		const payload = { username: user.username, sub: user.id, email: user.email };
		const access_token = this.jwtService.sign(payload);

		return {
			access_token,
			token_type: "Bearer",
			expires_in: 604800, // 7 dias
			user: this.mapToUserResponseDto(user),
		};
	}

	async register(registerDto: RegisterDto): Promise<AuthResponseDto> {
		// Verificar se email já existe
		const existingUser = await this.authRepository.findByUsernameOrEmail(registerDto.email);
		if (existingUser) {
			throw new ConflictException("Email ou nome de usuário já está em uso");
		}

		// Verificar se username já existe
		const existingUsername = await this.authRepository.findByUsernameOrEmail(registerDto.username);
		if (existingUsername) {
			throw new ConflictException("Email ou nome de usuário já está em uso");
		}

		// Hash da senha
		const hashedPassword = await bcrypt.hash(registerDto.password, 10);

		// Criar usuário
		const userData = {
			...registerDto,
			password: hashedPassword,
		};

		const user = await this.authRepository.create(userData);
		const { password, ...userWithoutPassword } = user;

		// Gerar token
		const payload = { username: user.username, sub: user.id, email: user.email };
		const access_token = this.jwtService.sign(payload);

		return {
			access_token,
			token_type: "Bearer",
			expires_in: 604800, // 7 dias
			user: this.mapToUserResponseDto(userWithoutPassword),
		};
	}

	private mapToUserResponseDto(user: Partial<User>): UserResponseDto {
		return {
			id: user.id,
			username: user.username,
			email: user.email,
			fullName: user.fullName,
			avatar: user.avatar,
			isActive: user.isActive,
			createdAt: user.createdAt,
			updatedAt: user.updatedAt,
		};
	}
}
