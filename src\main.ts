import { NestFactory } from "@nestjs/core";
import { ValidationPipe } from "@nestjs/common";
import { SwaggerModule, DocumentBuilder } from "@nestjs/swagger";
import { AppModule } from "./app.module";

async function bootstrap() {
	const app = await NestFactory.create(AppModule);

	// Configurar pipes globais
	app.useGlobalPipes(
		new ValidationPipe({
			whitelist: true,
			forbidNonWhitelisted: true,
			transform: true,
		})
	);

	// Configurar CORS
	app.enableCors();

	// Configurar Swagger
	const config = new DocumentBuilder()
		.setTitle("🚀 Backend Test API")
		.setDescription(
			`
## 📋 Descrição

API completa para autenticação e gerenciamento de usuários desenvolvida com NestJS, TypeScript e PostgreSQL.

### 🔗 Recursos Principais

- **🔐 Autenticação JWT**: Sistema seguro de login e registro
- **👥 Gerenciamento de Usuários**: CRUD completo de usuários
- **🛡️ Validação de Dados**: Validação robusta com class-validator
- **📊 Banco de Dados**: PostgreSQL com TypeORM
- **🏗️ Arquitetura Limpa**: Seguindo princípios SOLID e Clean Architecture

### 🔒 Autenticação

Esta API utiliza **JWT (JSON Web Tokens)** para autenticação. Para acessar endpoints protegidos:

1. Faça login através do endpoint \`/auth/login\`
2. Use o token retornado no header \`Authorization: Bearer {token}\`
3. O token é válido por 7 dias

### 📚 Como Usar

1. **Registrar**: Crie uma conta através de \`POST /auth/register\`
2. **Login**: Faça login através de \`POST /auth/login\`
3. **Gerenciar Usuários**: Use os endpoints de \`/users\` (requer autenticação)

### 🏷️ Códigos de Status

- \`200\` - Sucesso
- \`201\` - Criado com sucesso
- \`400\` - Dados inválidos
- \`401\` - Não autorizado
- \`403\` - Acesso negado
- \`404\` - Recurso não encontrado
- \`409\` - Conflito (ex: email já existe)
- \`500\` - Erro interno do servidor

### 🌐 Ambiente

- **Desenvolvimento**: \`http://localhost:3000\`
- **Documentação**: \`http://localhost:3000/api\`
		`
		)
		.setVersion("1.0.0")
		.setTermsOfService("https://example.com/terms")
		.setContact("Equipe de Desenvolvimento", "https://example.com/contact", "<EMAIL>")
		.setLicense("MIT", "https://opensource.org/licenses/MIT")
		.addServer("http://localhost:3000", "Servidor de Desenvolvimento")
		.addServer("https://api.example.com", "Servidor de Produção")
		.addBearerAuth(
			{
				type: "http",
				scheme: "bearer",
				bearerFormat: "JWT",
				name: "JWT",
				description: "Insira o token JWT no formato: Bearer {token}",
				in: "header",
			},
			"JWT-auth"
		)
		.addTag("auth", "🔐 Autenticação - Endpoints para login e registro de usuários")
		.addTag("users", "👥 Usuários - CRUD completo de gerenciamento de usuários")
		.build();

	const document = SwaggerModule.createDocument(app, config, {
		operationIdFactory: (controllerKey: string, methodKey: string) => methodKey,
		deepScanRoutes: true,
	});

	SwaggerModule.setup("api", app, document, {
		customSiteTitle: "Backend Test API - Documentação",
		customfavIcon: "/favicon.ico",
		customCss: `
			.swagger-ui .topbar { display: none }
			.swagger-ui .info .title { color: #2c3e50; font-size: 2.5em; }
			.swagger-ui .scheme-container { background: #f8f9fa; padding: 15px; border-radius: 5px; }
			.swagger-ui .info .description { font-size: 1.1em; line-height: 1.6; }
			.swagger-ui .opblock.opblock-post { border-color: #27ae60; }
			.swagger-ui .opblock.opblock-post .opblock-summary { border-color: #27ae60; }
			.swagger-ui .opblock.opblock-get { border-color: #3498db; }
			.swagger-ui .opblock.opblock-get .opblock-summary { border-color: #3498db; }
			.swagger-ui .opblock.opblock-patch { border-color: #f39c12; }
			.swagger-ui .opblock.opblock-patch .opblock-summary { border-color: #f39c12; }
			.swagger-ui .opblock.opblock-delete { border-color: #e74c3c; }
			.swagger-ui .opblock.opblock-delete .opblock-summary { border-color: #e74c3c; }
		`,
		swaggerOptions: {
			persistAuthorization: true,
			displayRequestDuration: true,
			docExpansion: "none",
			filter: true,
			showExtensions: true,
			showCommonExtensions: true,
			defaultModelsExpandDepth: 2,
			defaultModelExpandDepth: 2,
		},
	});

	const port = process.env.PORT || 3000;
	await app.listen(port);
	console.log(`🚀 Aplicação rodando na porta ${port}`);
	console.log(`📚 Documentação disponível em http://localhost:${port}/api`);
}
bootstrap();
