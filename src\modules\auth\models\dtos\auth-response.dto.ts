import { ApiProperty } from "@nestjs/swagger";
import { UserResponseDto } from "../../../user/models/dtos/user-response.dto";

export class AuthResponseDto {
	@ApiProperty({
		description: "Token de acesso JWT para autenticação em endpoints protegidos",
		example:
			"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************.xyz123abc",
		type: "string",
		format: "jwt",
	})
	access_token: string;

	@ApiProperty({
		description: "Tipo do token de autenticação",
		example: "Bearer",
		enum: ["Bearer"],
		default: "Bearer",
	})
	token_type: string;

	@ApiProperty({
		description: "Tempo de expiração do token em segundos (7 dias = 604.800 segundos)",
		example: 604800,
		type: "integer",
		minimum: 1,
	})
	expires_in: number;

	@ApiProperty({
		description: "Dados completos do usuário autenticado",
		type: () => UserResponseDto,
		example: {
			id: 1,
			username: "joa<PERSON>_silva",
			email: "<EMAIL>",
			fullName: "<PERSON>",
			avatar: "https://example.com/avatars/joao.jpg",
			isActive: true,
			createdAt: "2024-06-10T08:00:00.000Z",
			updatedAt: "2024-06-12T10:30:00.000Z",
		},
	})
	user: UserResponseDto;
}
