import { Injectable, Inject } from "@nestjs/common";
import { IAuthRepository } from "../models/interfaces/auth-repository.interface";
import { IUserRepository } from "../../user/models/interfaces/user-repository.interface";
import { User } from "../../user/models/entities/user.entity";

@Injectable()
export class AuthTypeOrmRepository implements IAuthRepository {
	constructor(
		@Inject("IUserRepository")
		private readonly userRepository: IUserRepository
	) {}

	async findByUsernameOrEmail(usernameOrEmail: string): Promise<User | null> {
		return this.userRepository.findByUsernameOrEmail(usernameOrEmail);
	}

	async create(userData: Partial<User>): Promise<User> {
		return this.userRepository.create(userData);
	}
}
