import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { UserController } from "./controllers/user.controller";
import { UserService } from "./services/user.service";
import { UserTypeOrmRepository } from "./repositories/user.repository";
import { User } from "./models/entities/user.entity";

@Module({
	imports: [TypeOrmModule.forFeature([User])],
	controllers: [UserController],
	providers: [
		UserService,
		UserTypeOrmRepository,
		{
			provide: "IUserRepository",
			useExisting: UserTypeOrmRepository,
		},
	],
	exports: ["IUserRepository", UserService],
})
export class UserModule {}
