import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, ParseIntPipe, HttpCode, HttpStatus } from "@nestjs/common";
import {
	ApiTags,
	ApiOperation,
	ApiResponse,
	ApiBearerAuth,
	ApiExtraModels,
	ApiBadRequestResponse,
	ApiUnauthorizedResponse,
	ApiNotFoundResponse,
	ApiConflictResponse,
	ApiInternalServerErrorResponse,
	ApiBody,
	ApiParam,
} from "@nestjs/swagger";
import { UserService } from "../services/user.service";
import { CreateUserDto } from "../models/dtos/create-user.dto";
import { UpdateUserDto } from "../models/dtos/update-user.dto";
import { UserResponseDto } from "../models/dtos/user-response.dto";
import { JwtAuthGuard } from "../../auth/models/guard/jwt-auth.guard";
import {
	ErrorResponseDto,
	ValidationErrorResponseDto,
	ConflictErrorResponseDto,
	UnauthorizedErrorResponseDto,
	NotFoundErrorResponseDto,
	InternalServerErrorResponseDto,
} from "../../../shared/dtos/error-response.dto";
import { DeleteSuccessResponseDto } from "../../../shared/dtos/success-response.dto";

@ApiTags("users")
@ApiExtraModels(
	UserResponseDto,
	CreateUserDto,
	UpdateUserDto,
	DeleteSuccessResponseDto,
	ErrorResponseDto,
	ValidationErrorResponseDto,
	ConflictErrorResponseDto,
	UnauthorizedErrorResponseDto,
	NotFoundErrorResponseDto,
	InternalServerErrorResponseDto
)
@Controller("users")
export class UserController {
	constructor(private readonly userService: UserService) {}

	@Post()
	@HttpCode(HttpStatus.CREATED)
	@ApiOperation({
		summary: "👤 Criar novo usuário",
		description: `
### 📋 Descrição
Cria um novo usuário no sistema. Este endpoint é público e pode ser usado como alternativa ao registro via \`/auth/register\`.

### 🔍 Validações Aplicadas
- **Username**: Único no sistema, 3-50 caracteres
- **Email**: Formato válido e único
- **Senha**: Mínimo 6 caracteres (será criptografada)
- **Nome Completo**: Opcional, máximo 100 caracteres
- **Avatar**: URL válida (opcional)

### 💡 Diferenças do /auth/register
- Não retorna token JWT automaticamente
- Foco na criação de usuários administrativamente
- Resposta contém apenas os dados do usuário criado
		`,
		operationId: "createUser",
	})
	@ApiBody({
		type: CreateUserDto,
		description: "Dados necessários para criar um novo usuário",
		examples: {
			adminUser: {
				summary: "Usuário Administrativo",
				description: "Criação de usuário com dados completos",
				value: {
					username: "admin_user",
					email: "<EMAIL>",
					password: "AdminPass@123",
					fullName: "Administrador do Sistema",
					avatar: "https://company.com/avatars/admin.jpg",
				},
			},
			basicUser: {
				summary: "Usuário Básico",
				description: "Criação de usuário com dados mínimos",
				value: {
					username: "new_user",
					email: "<EMAIL>",
					password: "SecurePass@456",
				},
			},
		},
	})
	@ApiResponse({
		status: 201,
		description: "✅ Usuário criado com sucesso",
		type: UserResponseDto,
		schema: {
			example: {
				id: 5,
				username: "admin_user",
				email: "<EMAIL>",
				fullName: "Administrador do Sistema",
				avatar: "https://company.com/avatars/admin.jpg",
				isActive: true,
				createdAt: "2024-06-12T10:30:00.000Z",
				updatedAt: "2024-06-12T10:30:00.000Z",
			},
		},
	})
	@ApiBadRequestResponse({
		description: "❌ Dados inválidos fornecidos",
		type: ValidationErrorResponseDto,
		schema: {
			example: {
				statusCode: 400,
				message: ["username deve ter pelo menos 3 caracteres", "email deve ser um email válido"],
				error: "Bad Request",
				timestamp: "2024-06-12T10:30:00.000Z",
				path: "/users",
			},
		},
	})
	@ApiConflictResponse({
		description: "⚠️ Email ou username já existe",
		type: ConflictErrorResponseDto,
		schema: {
			example: {
				statusCode: 409,
				message: "Email ou nome de usuário já está em uso",
				error: "Conflict",
				timestamp: "2024-06-12T10:30:00.000Z",
				path: "/users",
			},
		},
	})
	@ApiInternalServerErrorResponse({
		description: "🚫 Erro interno do servidor",
		type: InternalServerErrorResponseDto,
	})
	create(@Body() createUserDto: CreateUserDto): Promise<UserResponseDto> {
		return this.userService.create(createUserDto);
	}

	@Get()
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "📋 Listar todos os usuários",
		description: `
### 📋 Descrição
Retorna uma lista paginada de todos os usuários cadastrados no sistema.

### 🔐 Autenticação Necessária
Este endpoint requer autenticação JWT. Use o token obtido no login.

### 📊 Dados Retornados
- Lista completa de usuários (sem senhas)
- Informações de perfil de cada usuário
- Status de ativação
- Datas de criação e atualização

### 🔍 Filtros Futuros
Em versões futuras, este endpoint suportará:
- Paginação (\`?page=1&limit=10\`)
- Filtros por status (\`?active=true\`)
- Busca por nome (\`?search=joão\`)
		`,
		operationId: "getAllUsers",
	})
	@ApiResponse({
		status: 200,
		description: "✅ Lista de usuários obtida com sucesso",
		type: [UserResponseDto],
		schema: {
			example: [
				{
					id: 1,
					username: "joao_silva",
					email: "<EMAIL>",
					fullName: "João Silva",
					avatar: "https://example.com/avatars/joao.jpg",
					isActive: true,
					createdAt: "2024-06-10T08:00:00.000Z",
					updatedAt: "2024-06-11T14:30:00.000Z",
				},
				{
					id: 2,
					username: "maria_santos",
					email: "<EMAIL>",
					fullName: "Maria Santos",
					avatar: null,
					isActive: true,
					createdAt: "2024-06-11T09:15:00.000Z",
					updatedAt: "2024-06-11T09:15:00.000Z",
				},
			],
		},
	})
	@ApiUnauthorizedResponse({
		description: "🔒 Token não fornecido ou inválido",
		type: UnauthorizedErrorResponseDto,
		schema: {
			example: {
				statusCode: 401,
				message: "Token JWT não fornecido ou inválido",
				error: "Unauthorized",
				timestamp: "2024-06-12T10:30:00.000Z",
				path: "/users",
				code: "TOKEN_MISSING",
			},
		},
	})
	@ApiInternalServerErrorResponse({
		description: "🚫 Erro interno do servidor",
		type: InternalServerErrorResponseDto,
	})
	findAll(): Promise<UserResponseDto[]> {
		return this.userService.findAll();
	}

	@Get(":id")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "🔍 Buscar usuário por ID",
		description: `
### 📋 Descrição
Busca um usuário específico pelo seu ID único no sistema.

### 🔐 Autenticação Necessária
Este endpoint requer autenticação JWT.

### 📊 Dados Retornados
- Informações completas do usuário (sem senha)
- Status de ativação
- Timestamps de criação e atualização

### 🔍 Validações
- ID deve ser um número inteiro válido
- Usuário deve existir no sistema
		`,
		operationId: "getUserById",
	})
	@ApiParam({
		name: "id",
		type: "integer",
		description: "ID único do usuário no sistema",
		example: 1,
		schema: {
			minimum: 1,
		},
	})
	@ApiResponse({
		status: 200,
		description: "✅ Usuário encontrado com sucesso",
		type: UserResponseDto,
		schema: {
			example: {
				id: 1,
				username: "joao_silva",
				email: "<EMAIL>",
				fullName: "João Silva",
				avatar: "https://example.com/avatars/joao.jpg",
				isActive: true,
				createdAt: "2024-06-10T08:00:00.000Z",
				updatedAt: "2024-06-11T14:30:00.000Z",
			},
		},
	})
	@ApiBadRequestResponse({
		description: "❌ ID inválido fornecido",
		type: ValidationErrorResponseDto,
		schema: {
			example: {
				statusCode: 400,
				message: ["ID deve ser um número inteiro positivo"],
				error: "Bad Request",
				timestamp: "2024-06-12T10:30:00.000Z",
				path: "/users/abc",
			},
		},
	})
	@ApiUnauthorizedResponse({
		description: "🔒 Token não fornecido ou inválido",
		type: UnauthorizedErrorResponseDto,
	})
	@ApiNotFoundResponse({
		description: "🔍 Usuário não encontrado",
		type: NotFoundErrorResponseDto,
		schema: {
			example: {
				statusCode: 404,
				message: "Usuário não encontrado",
				error: "Not Found",
				timestamp: "2024-06-12T10:30:00.000Z",
				path: "/users/999",
				resourceId: "999",
				resourceType: "User",
			},
		},
	})
	@ApiInternalServerErrorResponse({
		description: "🚫 Erro interno do servidor",
		type: InternalServerErrorResponseDto,
	})
	findOne(@Param("id", ParseIntPipe) id: number): Promise<UserResponseDto> {
		return this.userService.findById(id);
	}

	@Patch(":id")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@ApiOperation({
		summary: "✏️ Atualizar usuário",
		description: `
### 📋 Descrição
Atualiza informações de um usuário existente. Apenas os campos fornecidos serão atualizados.

### 🔐 Autenticação Necessária
Este endpoint requer autenticação JWT.

### 🔍 Campos Atualizáveis
- **Nome Completo**: Máximo 100 caracteres
- **Avatar**: URL válida da imagem

### ⚠️ Campos NÃO Atualizáveis
- Username (único e imutável)
- Email (requer endpoint específico)
- Senha (requer endpoint específico)
- Timestamps (automáticos)

### 💡 Dica
Use PATCH para atualizações parciais. Apenas os campos enviados serão modificados.
		`,
		operationId: "updateUser",
	})
	@ApiParam({
		name: "id",
		type: "integer",
		description: "ID único do usuário a ser atualizado",
		example: 1,
	})
	@ApiBody({
		type: UpdateUserDto,
		description: "Dados para atualização (todos opcionais)",
		examples: {
			updateName: {
				summary: "Atualizar Nome",
				description: "Atualizar apenas o nome completo",
				value: {
					fullName: "João Silva Santos",
				},
			},
			updateAvatar: {
				summary: "Atualizar Avatar",
				description: "Atualizar apenas a foto do perfil",
				value: {
					avatar: "https://example.com/new-avatar.jpg",
				},
			},
			updateBoth: {
				summary: "Atualização Completa",
				description: "Atualizar nome e avatar",
				value: {
					fullName: "João Silva Santos",
					avatar: "https://example.com/new-avatar.jpg",
				},
			},
		},
	})
	@ApiResponse({
		status: 200,
		description: "✅ Usuário atualizado com sucesso",
		type: UserResponseDto,
		schema: {
			example: {
				id: 1,
				username: "joao_silva",
				email: "<EMAIL>",
				fullName: "João Silva Santos",
				avatar: "https://example.com/new-avatar.jpg",
				isActive: true,
				createdAt: "2024-06-10T08:00:00.000Z",
				updatedAt: "2024-06-12T10:30:00.000Z",
			},
		},
	})
	@ApiBadRequestResponse({
		description: "❌ Dados inválidos fornecidos",
		type: ValidationErrorResponseDto,
		schema: {
			example: {
				statusCode: 400,
				message: ["avatar deve ser uma URL válida", "fullName deve ter no máximo 100 caracteres"],
				error: "Bad Request",
				timestamp: "2024-06-12T10:30:00.000Z",
				path: "/users/1",
			},
		},
	})
	@ApiUnauthorizedResponse({
		description: "🔒 Token não fornecido ou inválido",
		type: UnauthorizedErrorResponseDto,
	})
	@ApiNotFoundResponse({
		description: "🔍 Usuário não encontrado",
		type: NotFoundErrorResponseDto,
	})
	@ApiInternalServerErrorResponse({
		description: "🚫 Erro interno do servidor",
		type: InternalServerErrorResponseDto,
	})
	update(@Param("id", ParseIntPipe) id: number, @Body() updateUserDto: UpdateUserDto): Promise<UserResponseDto> {
		return this.userService.update(id, updateUserDto);
	}

	@Delete(":id")
	@UseGuards(JwtAuthGuard)
	@ApiBearerAuth("JWT-auth")
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: "🗑️ Deletar usuário",
		description: `
### 📋 Descrição
Remove permanentemente um usuário do sistema. Esta ação é **irreversível**.

### 🔐 Autenticação Necessária
Este endpoint requer autenticação JWT.

### ⚠️ Atenção
- A exclusão é **permanente** e **irreversível**
- Todos os dados associados ao usuário serão perdidos
- Considere desativar o usuário ao invés de deletar

### 🔍 Validações
- Usuário deve existir no sistema
- Verificações de integridade referencial

### 💡 Alternativa
Para desativar temporariamente um usuário, considere usar um endpoint de atualização para definir \`isActive: false\`.
		`,
		operationId: "deleteUser",
	})
	@ApiParam({
		name: "id",
		type: "integer",
		description: "ID único do usuário a ser deletado",
		example: 1,
	})
	@ApiResponse({
		status: 200,
		description: "✅ Usuário deletado com sucesso",
		type: DeleteSuccessResponseDto,
		schema: {
			example: {
				success: true,
				message: "Usuário deletado com sucesso",
				deletedId: "1",
				deletedAt: "2024-06-12T10:30:00.000Z",
			},
		},
	})
	@ApiBadRequestResponse({
		description: "❌ ID inválido fornecido",
		type: ValidationErrorResponseDto,
	})
	@ApiUnauthorizedResponse({
		description: "🔒 Token não fornecido ou inválido",
		type: UnauthorizedErrorResponseDto,
	})
	@ApiNotFoundResponse({
		description: "🔍 Usuário não encontrado",
		type: NotFoundErrorResponseDto,
	})
	@ApiInternalServerErrorResponse({
		description: "🚫 Erro interno do servidor",
		type: InternalServerErrorResponseDto,
	})
	async remove(@Param("id", ParseIntPipe) id: number): Promise<DeleteSuccessResponseDto> {
		await this.userService.delete(id);
		return {
			success: true,
			message: "Usuário deletado com sucesso",
			deletedId: id.toString(),
			deletedAt: new Date().toISOString(),
		};
	}
}
