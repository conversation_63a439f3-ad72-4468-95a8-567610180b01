import { Injectable, ConflictException, NotFoundException, Inject } from "@nestjs/common";
import * as bcrypt from "bcryptjs";
import { IUserRepository } from "../models/interfaces/user-repository.interface";
import { CreateUserDto } from "../models/dtos/create-user.dto";
import { UpdateUserDto } from "../models/dtos/update-user.dto";
import { UserResponseDto } from "../models/dtos/user-response.dto";
import { User } from "../models/entities/user.entity";

@Injectable()
export class UserService {
	constructor(
		@Inject("IUserRepository")
		private readonly userRepository: IUserRepository
	) {}

	async create(createUserDto: CreateUserDto): Promise<UserResponseDto> {
		// Verificar se email já existe
		const existingEmail = await this.userRepository.findByEmail(createUserDto.email);
		if (existingEmail) {
			throw new ConflictException("Email já está em uso");
		}

		// Verificar se username já existe
		const existingUsername = await this.userRepository.findByUsername(createUserDto.username);
		if (existingUsername) {
			throw new ConflictException("Nome de usuário já está em uso");
		}

		// Hash da senha
		const hashedPassword = await bcrypt.hash(createUserDto.password, 10);

		// Criar usuário
		const userData = {
			...createUserDto,
			password: hashedPassword,
		};

		const user = await this.userRepository.create(userData);
		return this.mapToResponseDto(user);
	}

	async findAll(): Promise<UserResponseDto[]> {
		const users = await this.userRepository.findAll();
		return users.map(user => this.mapToResponseDto(user));
	}

	async findById(id: number): Promise<UserResponseDto> {
		const user = await this.userRepository.findById(id);
		if (!user) {
			throw new NotFoundException("Usuário não encontrado");
		}
		return this.mapToResponseDto(user);
	}

	async update(id: number, updateUserDto: UpdateUserDto): Promise<UserResponseDto> {
		const user = await this.userRepository.findById(id);
		if (!user) {
			throw new NotFoundException("Usuário não encontrado");
		}

		const updatedUser = await this.userRepository.update(id, updateUserDto);
		return this.mapToResponseDto(updatedUser);
	}

	async delete(id: number): Promise<void> {
		const user = await this.userRepository.findById(id);
		if (!user) {
			throw new NotFoundException("Usuário não encontrado");
		}

		await this.userRepository.delete(id);
	}

	async findByUsernameOrEmail(usernameOrEmail: string): Promise<User | null> {
		return this.userRepository.findByUsernameOrEmail(usernameOrEmail);
	}

	async validatePassword(password: string, hashedPassword: string): Promise<boolean> {
		return bcrypt.compare(password, hashedPassword);
	}

	private mapToResponseDto(user: User): UserResponseDto {
		const { password, ...userWithoutPassword } = user;
		return userWithoutPassword as UserResponseDto;
	}
}
