import { Controller, Post, Body, HttpCode, HttpStatus } from "@nestjs/common";
import {
	ApiTags,
	ApiOperation,
	ApiResponse,
	ApiExtraModels,
	ApiBadRequestResponse,
	ApiConflictResponse,
	ApiUnauthorizedResponse,
	ApiInternalServerErrorResponse,
	ApiBody,
} from "@nestjs/swagger";
import { AuthService } from "../services/auth.service";
import { LoginDto } from "../models/dtos/login.dto";
import { RegisterDto } from "../models/dtos/register.dto";
import { AuthResponseDto } from "../models/dtos/auth-response.dto";
import {
	ErrorResponseDto,
	ValidationErrorResponseDto,
	ConflictErrorResponseDto,
	UnauthorizedErrorResponseDto,
	InternalServerErrorResponseDto,
} from "../../../shared/dtos/error-response.dto";

@ApiTags("auth")
@ApiExtraModels(
	AuthResponseDto,
	ErrorResponseDto,
	ValidationErrorResponseDto,
	ConflictErrorResponseDto,
	UnauthorizedErrorResponseDto,
	InternalServerErrorResponseDto
)
@Controller("auth")
export class AuthController {
	constructor(private readonly authService: AuthService) {}

	@Post("register")
	@HttpCode(HttpStatus.CREATED)
	@ApiOperation({
		summary: "📝 Registrar novo usuário",
		description: `
### 📋 Descrição
Cria uma nova conta de usuário no sistema com validação completa dos dados.

### 🔍 Validações Aplicadas
- **Username**: Único, entre 3-50 caracteres
- **Email**: Formato válido e único no sistema
- **Senha**: Mínimo 6 caracteres
- **Nome Completo**: Opcional, máximo 100 caracteres
- **Avatar**: URL válida (opcional)

### 💡 Dicas
- O sistema verifica automaticamente duplicatas de email/username
- A senha é criptografada antes do armazenamento
- Um token JWT é retornado automaticamente após o registro
		`,
		operationId: "registerUser",
	})
	@ApiBody({
		type: RegisterDto,
		description: "Dados necessários para criar uma nova conta",
		examples: {
			basicUser: {
				summary: "Usuário Básico",
				description: "Exemplo de registro com dados mínimos obrigatórios",
				value: {
					username: "joao_silva",
					email: "<EMAIL>",
					password: "MinhaSenh@123",
				},
			},
			completeUser: {
				summary: "Usuário Completo",
				description: "Exemplo de registro com todos os dados opcionais",
				value: {
					username: "maria_santos",
					email: "<EMAIL>",
					password: "SenhaSegura@456",
					fullName: "Maria Santos Silva",
					avatar: "https://example.com/avatars/maria.jpg",
				},
			},
		},
	})
	@ApiResponse({
		status: 201,
		description: "✅ Usuário registrado com sucesso. Token JWT retornado para acesso imediato.",
		type: AuthResponseDto,
		schema: {
			example: {
				access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
				token_type: "Bearer",
				expires_in: 604800,
				user: {
					id: 1,
					username: "joao_silva",
					email: "<EMAIL>",
					fullName: "João Silva",
					avatar: null,
					isActive: true,
					createdAt: "2024-06-12T10:30:00.000Z",
					updatedAt: "2024-06-12T10:30:00.000Z",
				},
			},
		},
	})
	@ApiBadRequestResponse({
		description: "❌ Dados inválidos fornecidos",
		type: ValidationErrorResponseDto,
		schema: {
			example: {
				statusCode: 400,
				message: ["username deve ter pelo menos 3 caracteres", "email deve ser um email válido", "password deve ter pelo menos 6 caracteres"],
				error: "Bad Request",
				timestamp: "2024-06-12T10:30:00.000Z",
				path: "/auth/register",
			},
		},
	})
	@ApiConflictResponse({
		description: "⚠️ Email ou nome de usuário já existe no sistema",
		type: ConflictErrorResponseDto,
		schema: {
			example: {
				statusCode: 409,
				message: "Email ou nome de usuário já está em uso",
				error: "Conflict",
				timestamp: "2024-06-12T10:30:00.000Z",
				path: "/auth/register",
				details: {
					field: "email",
					value: "<EMAIL>",
					reason: "Este email já está cadastrado no sistema",
				},
			},
		},
	})
	@ApiInternalServerErrorResponse({
		description: "🚫 Erro interno do servidor",
		type: InternalServerErrorResponseDto,
		schema: {
			example: {
				statusCode: 500,
				message: "Erro interno do servidor. Tente novamente mais tarde.",
				error: "Internal Server Error",
				timestamp: "2024-06-12T10:30:00.000Z",
				path: "/auth/register",
				errorId: "err_123456789",
			},
		},
	})
	register(@Body() registerDto: RegisterDto): Promise<AuthResponseDto> {
		return this.authService.register(registerDto);
	}

	@Post("login")
	@HttpCode(HttpStatus.OK)
	@ApiOperation({
		summary: "🔑 Realizar login",
		description: `
### 📋 Descrição
Autentica um usuário existente e retorna um token JWT para acesso à API.

### 🔍 Processo de Autenticação
1. **Verificação**: Valida username/email e senha
2. **Autenticação**: Compara senha com hash armazenado
3. **Token JWT**: Gera token com validade de 7 dias
4. **Resposta**: Retorna token e dados do usuário

### 🔑 Formatos de Login Aceitos
- **Username**: \`joao_silva\`
- **Email**: \`<EMAIL>\`

### ⚡ Token JWT
- **Validade**: 7 dias (604.800 segundos)
- **Formato**: Bearer Token
- **Uso**: Header \`Authorization: Bearer {token}\`
		`,
		operationId: "loginUser",
	})
	@ApiBody({
		type: LoginDto,
		description: "Credenciais para autenticação",
		examples: {
			withUsername: {
				summary: "Login com Username",
				description: "Fazer login usando nome de usuário",
				value: {
					usernameOrEmail: "joao_silva",
					password: "MinhaSenh@123",
				},
			},
			withEmail: {
				summary: "Login com Email",
				description: "Fazer login usando endereço de email",
				value: {
					usernameOrEmail: "<EMAIL>",
					password: "MinhaSenh@123",
				},
			},
		},
	})
	@ApiResponse({
		status: 200,
		description: "✅ Login realizado com sucesso. Token JWT gerado.",
		type: AuthResponseDto,
		schema: {
			example: {
				access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
				token_type: "Bearer",
				expires_in: 604800,
				user: {
					id: 1,
					username: "joao_silva",
					email: "<EMAIL>",
					fullName: "João Silva",
					avatar: "https://example.com/avatars/joao.jpg",
					isActive: true,
					createdAt: "2024-06-10T08:00:00.000Z",
					updatedAt: "2024-06-12T10:30:00.000Z",
				},
			},
		},
	})
	@ApiBadRequestResponse({
		description: "❌ Dados de login inválidos",
		type: ValidationErrorResponseDto,
		schema: {
			example: {
				statusCode: 400,
				message: ["usernameOrEmail é obrigatório", "password é obrigatório"],
				error: "Bad Request",
				timestamp: "2024-06-12T10:30:00.000Z",
				path: "/auth/login",
			},
		},
	})
	@ApiUnauthorizedResponse({
		description: "🔒 Credenciais inválidas ou usuário não encontrado",
		type: UnauthorizedErrorResponseDto,
		schema: {
			example: {
				statusCode: 401,
				message: "Credenciais inválidas",
				error: "Unauthorized",
				timestamp: "2024-06-12T10:30:00.000Z",
				path: "/auth/login",
				code: "INVALID_CREDENTIALS",
			},
		},
	})
	@ApiInternalServerErrorResponse({
		description: "🚫 Erro interno do servidor",
		type: InternalServerErrorResponseDto,
		schema: {
			example: {
				statusCode: 500,
				message: "Erro interno do servidor. Tente novamente mais tarde.",
				error: "Internal Server Error",
				timestamp: "2024-06-12T10:30:00.000Z",
				path: "/auth/login",
				errorId: "err_987654321",
			},
		},
	})
	login(@Body() loginDto: LoginDto): Promise<AuthResponseDto> {
		return this.authService.login(loginDto);
	}
}
